<?php

namespace App\Services;

use Illuminate\Support\Facades\Redis;

class RateLimiterService
{
    /**
     * 检查是否超过限流
     *
     * @param string $key 限流键名
     * @param int $maxRequests 最大请求数
     * @param int $windowSeconds 时间窗口（秒）
     * @return bool true表示允许请求，false表示超过限制
     */
    public function checkRateLimit(string $key, int $maxRequests, int $windowSeconds): bool
    {
        $now = time();
        $windowStart = $now - $windowSeconds;

        // 移除过期的请求记录
        Redis::zremrangebyscore($key, 0, $windowStart);

        // 获取当前窗口内的请求数量
        $currentCount = Redis::zcard($key);

        // 如果当前请求数量已达到限制，则拒绝请求
        if ($currentCount >= $maxRequests) {
            return false;
        }

        // 添加当前请求时间戳
        Redis::zadd($key, $now, $now . '_' . uniqid());

        // 设置键的过期时间
        Redis::expire($key, $windowSeconds + 1);

        // 允许请求
        return true;
    }

    /**
     * 获取当前请求数量
     *
     * @param string $key 限流键名
     * @param int $windowSeconds 时间窗口（秒）
     * @return int 当前窗口内的请求数量
     */
    public function getCurrentCount(string $key, int $windowSeconds): int
    {
        $now = time();
        $windowStart = $now - $windowSeconds;

        // 移除过期的请求记录
        Redis::zremrangebyscore($key, 0, $windowStart);

        // 返回当前窗口内的请求数量
        return Redis::zcard($key);
    }

    /**
     * 重置限流计数器
     *
     * @param string $key 限流键名
     * @return bool
     */
    public function resetRateLimit(string $key): bool
    {
        return Redis::del($key) > 0;
    }
}
