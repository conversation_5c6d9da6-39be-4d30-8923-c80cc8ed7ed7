<?php

namespace App\Console\Commands;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Services\HaiboService;
use App\Services\RiderLocationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class HaiboCallback extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'haibo:callback {order_no : 订单号}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '手动触发海博配送状态回调';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orderNo = $this->argument('order_no');

        $this->info("开始处理订单号: {$orderNo}");

        try {
            // 查找订单
            $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

            if (!$order) {
                $this->error("订单不存在: {$orderNo}");
                return Command::FAILURE;
            }

            $this->info("找到订单: {$order->order_no}");
            $this->info("订单状态: " . ($order->StatusMap[$order->order_status] ?? '未知'));
            $this->info("应用标识: {$order->app_key}");

            // 检查是否为海博订单
            if ($order->app_key !== O2oErrandOrder::APP_KEY_HB) {
                $this->error("该订单不是海博订单，app_key: {$order->app_key}");
                return Command::FAILURE;
            }

            // 获取骑手信息
            $rider = null;
            if ($order->rider_id) {
                $rider = Rider::query()->where("id", $order->rider_id)->first();
                if ($rider) {
                    $this->info("骑手信息: {$rider->name} ({$rider->phone})");
                } else {
                    $this->warn("未找到骑手信息，rider_id: {$order->rider_id}");
                }
            } else {
                $this->warn("订单未分配骑手");
            }

            // 获取骑手位置
            $location = null;
            if ($order->rider_id) {
                $riderLocationService = app(RiderLocationService::class);
                $location = $riderLocationService->getRiderLocation($order->rider_id);
                if ($location) {
                    $this->info("骑手位置: 经度 {$location['lng']}, 纬度 {$location['lat']}");
                } else {
                    $this->warn("未获取到骑手位置信息");
                }
            }

            // 构建其他数据
            $otherData = [
                "rider_name" => $rider ? $rider->name : "",
                "rider_phone" => $rider ? $rider->phone : "",
                "longitude" => $location ? $location['lng'] : "",
                "latitude" => $location ? $location['lat'] : "",
            ];

            $this->info("其他数据: " . json_encode($otherData, JSON_UNESCAPED_UNICODE));

            // 获取海博状态码
            $haiboService = new HaiboService();
            $haiboStatus = $haiboService->getHaiboStatus($order);

            $this->info("海博状态码: {$haiboStatus}");

            // 确认是否执行回调
            if (!$this->confirm('确认执行海博配送状态回调？')) {
                $this->info('操作已取消');
                return Command::SUCCESS;
            }

            // 执行海博配送状态回调
            $this->info("正在执行海博配送状态回调...");

            $result = $haiboService->deliveryStatusCallback(
                $order->user_id,
                $order->order_no,
                $order->out_order_no,
                $haiboStatus,
                $otherData
            );

            if ($result) {
                $this->info("✅ 海博配送状态回调执行成功");
                Log::info("手动执行海博回调成功", [
                    'order_no' => $order->order_no,
                    'out_order_no' => $order->out_order_no,
                    'status' => $haiboStatus,
                    'other_data' => $otherData
                ]);
            } else {
                $this->error("❌ 海博配送状态回调执行失败");
                Log::error("手动执行海博回调失败", [
                    'order_no' => $order->order_no,
                    'out_order_no' => $order->out_order_no,
                    'status' => $haiboStatus,
                    'other_data' => $otherData
                ]);
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error("执行过程中发生错误: " . $e->getMessage());
            Log::error("手动执行海博回调异常", [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
